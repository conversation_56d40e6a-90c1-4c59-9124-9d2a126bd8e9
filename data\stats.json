{"games_played": 24, "total_winners": 24, "total_prize_pool": 3231, "player_count": 12, "average_game_duration": 301.858206436683, "top_players": [], "number_frequencies": {}, "session_start_time": 1753126350.28289, "recent_activity": [{"time": "23:47", "event": "Player 'Credit-Based Game' won with credit_based"}, {"time": "23:47", "event": "Player 'Credit Deduction Game' won with credit_based_recording"}, {"time": "23:30", "event": "Player 'Credit-Based Game' won with credit_based"}, {"time": "23:30", "event": "Player 'Credit Deduction Game' won with credit_based_recording"}, {"time": "23:29", "event": "Game started with 7 players"}, {"time": "23:29", "event": "Player 'Credit-Based Game' won with credit_based"}, {"time": "23:28", "event": "Player 'Credit Deduction Game' won with credit_based_recording"}, {"time": "23:28", "event": "Game started with 7 players"}, {"time": "23:27", "event": "Player 'Credit-Based Game' won with credit_based"}, {"time": "23:27", "event": "Player 'Credit Deduction Game' won with credit_based_recording"}]}