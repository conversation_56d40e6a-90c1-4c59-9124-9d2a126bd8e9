#!/usr/bin/env python3
"""
Test script to verify that real games are displayed correctly in the stats page.
"""

import sys
import os
import sqlite3

def test_real_game_display():
    """Test that real games are displayed correctly."""
    print("=" * 80)
    print("TESTING REAL GAME DISPLAY FIX")
    print("=" * 80)
    
    # Step 1: Check database content
    print("1. CHECKING DATABASE CONTENT:")
    try:
        db_path = os.path.join('data', 'stats.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all games
        cursor.execute('SELECT id, date_time, username, house, stake, players, total_calls, fee FROM game_history ORDER BY date_time DESC')
        all_games = cursor.fetchall()
        
        print(f"   Total games in database: {len(all_games)}")
        
        # Count different types
        credit_games = [g for g in all_games if 'Credit-Based Game' in g[2]]
        test_games = [g for g in all_games if 'TestPlayer' in g[2]]
        
        print(f"   Credit-based games: {len(credit_games)}")
        print(f"   Test games: {len(test_games)}")
        
        # Show sample credit-based games
        print(f"\n   Sample credit-based games:")
        for i, game in enumerate(credit_games[:3], 1):
            game_id, date_time, username, house, stake, players, calls, fee = game
            print(f"     {i}. {username} | {date_time} | {players} players | {stake} stake | {fee} fee")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ Error checking database: {e}")
        return False
    
    # Step 2: Test the session ID generation logic
    print("\n2. TESTING SESSION ID GENERATION:")
    try:
        # Simulate the new session ID logic
        test_games = [
            {'username': 'Credit-Based Game #15', 'players': 4},
            {'username': 'Credit-Based Game #14', 'players': 3},
            {'username': 'Credit-Based Game #13', 'players': 1},
            {'username': 'TestPlayer', 'players': 2},
        ]
        
        for game in test_games:
            username = game.get('username', 'Unknown')
            if 'Credit-Based Game' in username:
                try:
                    game_num = username.split('#')[-1] if '#' in username else '?'
                    session_id = f"Game #{game_num}"
                except:
                    session_id = username
            else:
                session_id = username
            
            print(f"   {username} → {session_id}")
        
        print("   ✅ Session ID generation working correctly")
        
    except Exception as e:
        print(f"   ❌ Error testing session ID: {e}")
        return False
    
    # Step 3: Test winner pattern logic
    print("\n3. TESTING WINNER PATTERN LOGIC:")
    try:
        test_games = [
            {'username': 'Credit-Based Game #15', 'players': 4, 'total_calls': 25},
            {'username': 'Credit-Based Game #14', 'players': 3, 'total_calls': 30},
            {'username': 'Credit-Based Game #13', 'players': 1, 'total_calls': 20},
            {'username': 'Credit-Based Game #12', 'players': 6, 'total_calls': 35},
        ]
        
        for game in test_games:
            username = game.get('username', '')
            players = game.get('players', 0)
            
            if 'Credit-Based Game' in username:
                if players == 1:
                    pattern = "Solo Game"
                elif players <= 2:
                    pattern = "Small Game"
                elif players <= 4:
                    pattern = "Standard Game"
                else:
                    pattern = "Large Game"
            else:
                pattern = "Main House"
            
            print(f"   {username} ({players} players) → {pattern}")
        
        print("   ✅ Winner pattern logic working correctly")
        
    except Exception as e:
        print(f"   ❌ Error testing winner pattern: {e}")
        return False
    
    # Step 4: Test the stats page game loading
    print("\n4. TESTING STATS PAGE GAME LOADING:")
    try:
        from stats_page import CentralizedStatsProvider
        
        provider = CentralizedStatsProvider()
        
        # Get game history (this should now show real games)
        games = provider.get_game_history()
        
        print(f"   Games loaded by stats provider: {len(games)}")
        
        # Check if we have credit-based games
        credit_games_loaded = [g for g in games if 'Credit-Based Game' in g.get('username', '')]
        print(f"   Credit-based games loaded: {len(credit_games_loaded)}")
        
        # Show sample loaded games
        print(f"\n   Sample loaded games:")
        for i, game in enumerate(games[:5], 1):
            username = game.get('username', 'Unknown')
            players = game.get('players', 0)
            stake = game.get('stake', 0)
            print(f"     {i}. {username} | {players} players | {stake} stake")
        
        if len(credit_games_loaded) > 0:
            print("   ✅ Credit-based games are being loaded correctly")
        else:
            print("   ❌ Credit-based games are not being loaded")
            return False
        
    except Exception as e:
        print(f"   ❌ Error testing stats page loading: {e}")
        return False
    
    # Step 5: Simulate the display transformation
    print("\n5. TESTING DISPLAY TRANSFORMATION:")
    try:
        # Simulate what the stats page will show
        sample_games = credit_games[:5] if len(credit_games) >= 5 else credit_games
        
        print(f"   Simulating display for {len(sample_games)} games:")
        print(f"   {'SESSION':<12} {'WINNER PATTERN':<15} {'STAKE':<8} {'PLAYERS':<8} {'FEE':<8}")
        print(f"   {'-'*12} {'-'*15} {'-'*8} {'-'*8} {'-'*8}")
        
        for i, game in enumerate(sample_games):
            game_id, date_time, username, house, stake, players, calls, fee = game
            
            # Generate session ID
            if 'Credit-Based Game' in username:
                try:
                    game_num = username.split('#')[-1] if '#' in username else str(i+1)
                    session_id = f"Game #{game_num}"
                except:
                    session_id = username
            else:
                session_id = username
            
            # Generate winner pattern
            if 'Credit-Based Game' in username:
                if players == 1:
                    pattern = "Solo Game"
                elif players <= 2:
                    pattern = "Small Game"
                elif players <= 4:
                    pattern = "Standard Game"
                else:
                    pattern = "Large Game"
            else:
                pattern = "Main House"
            
            print(f"   {session_id:<12} {pattern:<15} {stake:<8} {players:<8} {fee:<8}")
        
        print("   ✅ Display transformation working correctly")
        
    except Exception as e:
        print(f"   ❌ Error testing display transformation: {e}")
        return False
    
    # Summary
    print("\n" + "=" * 80)
    print("REAL GAME DISPLAY FIX SUMMARY")
    print("=" * 80)
    print("✅ Database contains correct credit-based games")
    print("✅ Session ID generation shows 'Game #X' instead of 'Session-X'")
    print("✅ Winner patterns show meaningful game types")
    print("✅ Stats provider loads credit-based games correctly")
    print("✅ Display transformation works as expected")
    
    print("\nExpected changes in stats page:")
    print("• SESSION column: 'Game #15', 'Game #14', etc. (instead of Session-10, Session-9)")
    print("• WINNER PATTERN: 'Standard Game', 'Solo Game', etc. (instead of Main House)")
    print("• All your real games should be visible")
    print("• Test games should be filtered out")
    
    return True

if __name__ == "__main__":
    success = test_real_game_display()
    if success:
        print("\n🎉 Real game display fix test successful!")
        print("\nTo see the changes:")
        print("1. Run the main game")
        print("2. Go to stats page")
        print("3. You should now see 'Game #15', 'Game #14', etc. instead of 'Session-X'")
        print("4. Winner patterns should show game types instead of 'Main House'")
    else:
        print("\n❌ Real game display fix test failed")
    
    sys.exit(0 if success else 1)
