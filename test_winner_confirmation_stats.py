#!/usr/bin/env python3
"""
Test script to verify that winner confirmation properly records game completion stats.

This script simulates the game completion flow when a winner is confirmed
to ensure that the STATS SYNC FIX is working correctly.
"""

import os
import sys
import time
import sqlite3
from datetime import datetime

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_winner_confirmation_stats():
    """Test that winner confirmation properly records game completion stats."""
    
    print("=" * 80)
    print("TESTING WINNER CONFIRMATION STATS RECORDING")
    print("=" * 80)
    
    # Check initial state
    conn = sqlite3.connect('data/stats.db')
    cursor = conn.cursor()
    
    today = datetime.now().strftime('%Y-%m-%d')
    
    # Get initial counts
    cursor.execute('SELECT COUNT(*) FROM game_history WHERE date(date_time) = ?', (today,))
    initial_count = cursor.fetchone()[0]
    
    cursor.execute('SELECT SUM(fee) FROM game_history WHERE date(date_time) = ?', (today,))
    initial_earnings = cursor.fetchone()[0] or 0
    
    print(f"Initial state for {today}:")
    print(f"  Games: {initial_count}")
    print(f"  Earnings: {initial_earnings}")
    print()
    
    # Simulate a game completion with winner confirmation
    print("Simulating game completion with winner confirmation...")
    
    try:
        # Import the game state handler
        from game_state_handler import GameState
        
        # Create a mock game object
        class MockGame:
            def __init__(self):
                self.players = [1, 2, 3, 4]  # 4 players
                self.called_numbers = [1, 5, 12, 23, 34, 45, 56, 67, 78, 89]  # 10 numbers called
                self.bet_amount = 30
                self.prize_pool = 96  # 4 players * 30 ETB * 0.8
                self.is_demo_mode = False
                self.game_started = True
                
            def reset_game(self):
                print("Mock game reset called")
                return True
        
        # Create mock game and game state handler
        mock_game = MockGame()
        game_state = GameState(mock_game)
        
        # Set up the winner validation state
        game_state.show_winner_display = True
        game_state.validation_result = True
        game_state.player_claim_cartella = 123
        game_state.winner_pattern = "Full House"
        game_state.is_paused = True
        
        print("Mock game state set up:")
        print(f"  Players: {len(mock_game.players)}")
        print(f"  Called numbers: {len(mock_game.called_numbers)}")
        print(f"  Bet amount: {mock_game.bet_amount}")
        print(f"  Winner cartella: {game_state.player_claim_cartella}")
        print(f"  Winner pattern: {game_state.winner_pattern}")
        print()
        
        # Call the close_validation_display method (this should record the game)
        print("Calling close_validation_display() to simulate winner confirmation...")
        game_state.close_validation_display()
        
        # Wait a moment for any async operations
        time.sleep(1)
        
        # Check the results
        cursor.execute('SELECT COUNT(*) FROM game_history WHERE date(date_time) = ?', (today,))
        final_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT SUM(fee) FROM game_history WHERE date(date_time) = ?', (today,))
        final_earnings = cursor.fetchone()[0] or 0
        
        # Get the most recent game record
        cursor.execute('''
        SELECT username, total_calls, fee, stake, players, details 
        FROM game_history 
        WHERE date(date_time) = ? 
        ORDER BY id DESC LIMIT 1
        ''', (today,))
        
        latest_game = cursor.fetchone()
        
        print("Results after winner confirmation:")
        print(f"  Games: {final_count} (was {initial_count})")
        print(f"  Earnings: {final_earnings} (was {initial_earnings})")
        
        if latest_game:
            print(f"  Latest game: {latest_game[0]}")
            print(f"    Calls: {latest_game[1]}")
            print(f"    Fee: {latest_game[2]}")
            print(f"    Stake: {latest_game[3]}")
            print(f"    Players: {latest_game[4]}")
            
            # Parse the details JSON to see the full game data
            try:
                import json
                details = json.loads(latest_game[5])
                print(f"    Completion type: {details.get('completion_type', 'unknown')}")
                print(f"    Claim type: {details.get('claim_type', 'unknown')}")
            except:
                print(f"    Details: {latest_game[5][:100]}...")
        
        # Verify the fix worked
        games_added = final_count - initial_count
        earnings_added = final_earnings - initial_earnings
        
        print()
        if games_added > 0:
            print(f"✅ SUCCESS: {games_added} game(s) were recorded")
            print(f"✅ SUCCESS: {earnings_added} ETB in earnings were added")
            
            if latest_game and "Cartella #123" in latest_game[0]:
                print("✅ SUCCESS: Winner was correctly recorded as 'Cartella #123'")
            else:
                print("⚠️  WARNING: Winner name doesn't match expected 'Cartella #123'")
                
        else:
            print("❌ FAILURE: No games were recorded")
            print("❌ This indicates the STATS SYNC FIX is not working")
        
    except Exception as e:
        print(f"❌ ERROR during test: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        conn.close()
    
    print()
    print("=" * 80)
    print("TEST COMPLETE")
    print("=" * 80)

if __name__ == "__main__":
    test_winner_confirmation_stats()
